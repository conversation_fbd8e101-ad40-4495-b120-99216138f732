import React, { useEffect, useRef, useState, useCallback } from 'react';
import Header from '../components/interview/Header';
import InterviewContent from '../components/interview/InterviewContent';
import ControlBar from '../components/interview/ControlBar';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../stores/authStore';
import { useToastContext } from '../contexts/ToastContext';
import { useBalance } from '../hooks/useBalance';
import { checkAndDeductCredits } from '../lib/api/credits';
import { useLocation } from 'react-router-dom';
import useDocumentTitle from '../hooks/useDocumentTitle';
import { useMockSession, useMockMessages, useMockSessionActions } from '../stores/mockInterviewStore';
import { InterviewSessionManager } from '../managers/InterviewSessionManager';

const MockInterviewSessionPage: React.FC = () => {
  // 设置页面标题
  useDocumentTitle('AI模拟面试');

  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuthStore();
  const { showError, showSuccess } = useToastContext();
  const { hasEnoughCredits, deductCredits, refreshBalance } = useBalance();

  // 使用ref来跟踪是否已经显示过通知，避免重复显示
  const hasShownNotification = useRef(false);
  // 跟踪是否已经进行过扣费验证
  const hasDeducted = useRef(false);

  // AI模拟面试状态
  const [isInterviewStarted, setIsInterviewStarted] = React.useState(false);

  // 使用真实的mock interview store
  const session = useMockSession();
  const messages = useMockMessages();
  const { initializeSession, endSession, addMessage, updateMessage, updateElapsedTime, setCurrentQuestionId } = useMockSessionActions();

  // 使用统一面试会话管理器
  const sessionManager = InterviewSessionManager.getInstance();
  const [isConnected, setIsConnected] = useState(false);

  // 🔥 新增：进度气泡状态管理
  const [progressBubbleId, setProgressBubbleId] = React.useState<string | null>(null);

  // 🔥 新增：处理进度消息的函数
  const handleProgressMessage = useCallback((content: string) => {
    // 识别进度消息并计算进度百分比
    let progress = 0;
    if (content.includes('面试正在初始化中')) {
      progress = 33;
    } else if (content.includes('正在生成您的专属面试题')) {
      progress = 67;
    } else if (content.includes('面试题生成完成')) {
      progress = 100;
    } else {
      return false; // 不是进度消息
    }

    if (progressBubbleId) {
      // 更新现有的进度气泡
      updateMessage(progressBubbleId, (msg) => ({
        ...msg,
        content,
        progress,
        timestamp: Date.now()
      }));
      console.log('🔄 Updating progress bubble:', { content, progress });
    } else {
      // 创建新的进度气泡
      const newProgressBubble = {
        id: `progress-bubble-${Date.now()}`,
        content,
        type: 'system' as const,
        timestamp: Date.now(),
        progress,
        isFinal: true
      };

      addMessage(newProgressBubble);
      setProgressBubbleId(newProgressBubble.id);
      console.log('✅ Created new progress bubble:', newProgressBubble);
    }

    return true; // 已处理
  }, [progressBubbleId, updateMessage, addMessage]);

  // 初始化会话数据并自动启动面试
  React.useEffect(() => {
    if (location.state) {
      // 🔥 生成唯一的会话ID
      const sessionId = `mock-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      initializeSession({
        id: sessionId,
        companyName: location.state.companyName || '模拟公司',
        positionName: location.state.positionName || '模拟岗位',
        startTime: Date.now(),
      });

      // 🔥 Mock模式：初始化面试会话，等待WebSocket连接事件
      console.log('🎯 Initializing mock interview session:', sessionId);
      setIsInterviewStarted(true);

      // 🔥 性能监控：记录启动开始时间
      const startTime = performance.now();
      (window as any).mockInterviewStartTime = startTime;

      console.log('⏳ Waiting for WebSocket connection event to start interview...');
    }
  }, [location.state, initializeSession]);

  // 启动Mock面试的函数
  const startMockInterview = useCallback(async () => {
    try {
      console.log('🔗 Starting mock interview with unified SessionManager');

      // 获取当前会话ID
      const currentSessionId = sessionManager.getSessionId();

      // 检查会话状态
      if (!currentSessionId) {
        console.error('❌ No active session found');
        showError('会话未建立，请重试');
        return;
      }

      console.log('🔍 Using SessionManager sessionId:', currentSessionId);

      // 获取WebSocket管理器并发送start_mock_interview消息
      const webSocketManager = sessionManager.getWebSocketManager();
      const message = {
        type: 'start_mock_interview',
        sessionId: currentSessionId,
        config: {
          companyName: location.state?.companyName || '模拟公司',
          positionName: location.state?.positionName || '模拟岗位',
          interviewLanguage: 'chinese',
          answerStyle: 'conversational'
        }
      };

      webSocketManager.sendMessage(currentSessionId, message);
      console.log('✅ Start mock interview message sent via SessionManager');
      setIsConnected(true);

    } catch (error) {
      console.error('❌ Failed to start mock interview:', error);
      showError('启动面试失败，请重试');
    }
  }, [sessionManager, location.state, showError]);

  // 组件卸载时清理
  React.useEffect(() => {
    return () => {
      console.log('🧹 MockInterviewSessionPage unmounting');
      // WebSocketManager会自动处理连接清理
    };
  }, []);

  // 计时器useEffect - 当WebSocket连接成功且面试开始后启动
  useEffect(() => {
    if (!session.isActive || !isConnected) return;

    console.log('🕐 Starting interview timer');
    const intervalId = setInterval(() => {
      const now = Date.now();
      const elapsed = now - session.startTime;
      updateElapsedTime(elapsed);
    }, 1000);

    return () => {
      console.log('🕐 Stopping interview timer');
      clearInterval(intervalId);
    };
  }, [session.isActive, session.startTime, isConnected, updateElapsedTime]);

  // 格式化时间显示
  const formatTime = (elapsedTime: number) => {
    const minutes = Math.floor(elapsedTime / 60000);
    const seconds = Math.floor((elapsedTime % 60000) / 1000);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const formattedTime = formatTime(session.elapsedTime);

  // AI模拟面试不需要音频处理
  const isListening = false; // mock模式不使用音频

  const toggleListening = () => {
    // mock模式不需要音频控制
    console.log('🎤 Mock mode does not use audio recording');
  };

  const refreshSession = () => {
    console.log('🔄 Mock interview session refresh');
    setIsConnected(false);
    setIsInterviewStarted(false);

    // 重新启动面试
    setTimeout(() => {
      startMockInterview();
    }, 1000);
  };

  const endInterview = () => {
    console.log('Mock end interview');
    endSession();
    navigate('/');
  };

  // 监听WebSocket连接状态变化并主动检查连接状态
  React.useEffect(() => {
    const handleConnectionChange = (event: CustomEvent) => {
      console.log('🔄 MockInterviewSessionPage: WebSocket connection state changed:', event.detail);

      if (event.detail.connected && event.detail.sessionId) {
        console.log('✅ WebSocket connected via event, starting mock interview automatically');
        // 延迟一下确保状态完全同步
        setTimeout(() => {
          startMockInterview();
        }, 100);
      }
    };

    // 主动检查当前会话连接状态
    const checkCurrentConnectionState = () => {
      console.log('🔍 MockInterviewSessionPage: Checking current session connection state');
      const currentSessionId = sessionManager.getSessionId();
      const webSocketManager = sessionManager.getWebSocketManager();
      const connection = webSocketManager.getSessionConnection(currentSessionId || '');

      console.log('🔍 SessionManager state:', {
        currentSessionId,
        connectionStatus: connection?.status || 'not found'
      });

      if (currentSessionId && connection && connection.status === 'connected') {
        console.log('✅ Session already connected, starting mock interview immediately');
        startMockInterview();
      } else {
        console.log('⏳ Session not ready, waiting for connection event...');
      }
    };

    // 监听WebSocket连接状态变化
    window.addEventListener('websocket-connection-change', handleConnectionChange as EventListener);

    // 延迟检查连接状态，确保组件完全挂载
    const checkTimer = setTimeout(checkCurrentConnectionState, 200);

    return () => {
      window.removeEventListener('websocket-connection-change', handleConnectionChange as EventListener);
      clearTimeout(checkTimer);
    };
  }, [startMockInterview, sessionManager]);

  // 监听WebSocket消息
  React.useEffect(() => {
    const handleMessage = (event: CustomEvent) => {
      const { sessionId, data } = event.detail;
      console.log('📥 Received WebSocket message via SessionManager:', data);

      // 🔥 新增：处理面试会话启动消息
      if (data.type === 'mock_interview_session_start') {
        console.log('🎯 Mock interview session started');
        setIsConnected(true);

        if (data.status === 'initializing') {
          const handled = handleProgressMessage('面试正在初始化中，正在为您生成专属面试题...');
          if (!handled) {
            // 如果handleProgressMessage没有处理，则使用原来的逻辑
            const initMessage = {
              id: `init-${Date.now()}`,
              content: '面试正在初始化中，正在为您生成专属面试题...',
              type: 'system' as const,
              timestamp: Date.now(),
              isFinal: true
            };
            addMessage(initMessage);
          }
        }
      }

      // 🔥 新增：处理初始化进度消息
      if (data.type === 'mock_interview_initializing') {
        console.log('🔄 Mock interview initializing');
        const handled = handleProgressMessage('正在生成您的专属面试题，请稍候...');
        if (!handled) {
          const progressMessage = {
            id: `progress-${Date.now()}`,
            content: '正在生成您的专属面试题，请稍候...',
            type: 'system' as const,
            timestamp: Date.now(),
            isFinal: true
          };
          addMessage(progressMessage);
        }
      }

      // 🔥 新增：处理准备状态消息
      if (data.type === 'mock_interview_preparing') {
        console.log('⚡ Mock interview preparing');
        const handled = handleProgressMessage('面试题生成完成，准备开始面试...');
        if (!handled) {
          const preparingMessage = {
            id: `preparing-${Date.now()}`,
            content: '面试题生成完成，准备开始面试...',
            type: 'system' as const,
            timestamp: Date.now(),
            isFinal: true
          };
          addMessage(preparingMessage);
        }
      }

      // 🔥 新增：处理初始化失败消息
      if (data.type === 'mock_interview_initialization_failed') {
        console.error('❌ Mock interview initialization failed');
        const errorMessage = {
          id: `error-${Date.now()}`,
          content: '面试初始化失败，请刷新页面重试。如果问题持续存在，请联系客服。',
          type: 'system' as const,
          timestamp: Date.now(),
          isFinal: true
        };
        addMessage(errorMessage);
        showError('面试初始化失败，请重试');
        setIsConnected(false);
      }

      if (data.type === 'mock_interview_question') {
        // 🔥 性能监控：记录第一个问题到达时间
        const questionTime = performance.now();
        const startTime = (window as any).mockInterviewStartTime;
        if (startTime) {
          console.log(`🎯 First question received in ${(questionTime - startTime).toFixed(2)}ms`);
          // 清除监控数据
          delete (window as any).mockInterviewStartTime;
        }

        // 🔥 保存当前问题ID，用于后续回答
        if (data.questionId) {
          setCurrentQuestionId(data.questionId);
          console.log(`📝 Saved current questionId: ${data.questionId}`);
        }

        // AI问题显示为蓝色气泡
        const questionMessage = {
          id: `ai-question-${Date.now()}`,
          content: data.questionText || data.content,
          type: 'interviewer' as const,
          timestamp: Date.now(),
          isFinal: true
        };
        addMessage(questionMessage);
        showSuccess('AI面试官已发送问题');
      }

      // 🔥 新增：处理评价和下一个问题的组合消息
      if (data.type === 'mock_interview_evaluation_and_question') {
        console.log('📨 Received evaluation and next question:', data);

        // 🔥 保存下一个问题的ID
        if (data.nextQuestion && data.nextQuestion.questionId) {
          setCurrentQuestionId(data.nextQuestion.questionId);
          console.log(`📝 Saved next questionId: ${data.nextQuestion.questionId}`);
        }

        // 🔥 将评价和问题合并成一个字符串，用换行符分隔
        // 这样现有的MessageBubble组件可以直接渲染，无需改动UI
        const combinedContent = `📊 回答评价

评分：${data.evaluation.score}/100

优点：
${data.evaluation.strengths.map(s => `• ${s}`).join('\n')}

改进建议：
${data.evaluation.improvements.map(i => `• ${i}`).join('\n')}

总体评价： ${data.evaluation.overallAssessment}



❓ 下一个问题

${data.nextQuestion.questionText}`;

        // 添加组合消息（显示为一个蓝色气泡）
        const combinedMessage = {
          id: `ai-evaluation-question-${Date.now()}`,
          content: combinedContent,
          type: 'interviewer' as const,
          timestamp: Date.now(),
          isFinal: true
        };
        addMessage(combinedMessage);
        showSuccess('AI面试官已发送评价和下一个问题');
      }

      // 处理单独的反馈消息（用于最后一个问题）
      if (data.type === 'mock_interview_feedback') {
        console.log('📨 Received final feedback:', data);

        const feedbackMessage = {
          id: `ai-feedback-${Date.now()}`,
          content: `最终评分：${data.feedback.score}/100

优点：
${data.feedback.strengths.map(s => `• ${s}`).join('\n')}

改进建议：
${data.feedback.improvements.map(i => `• ${i}`).join('\n')}

总体评价：${data.feedback.overallAssessment}`,
          type: 'interviewer' as const,
          timestamp: Date.now(),
          isFinal: true
        };
        addMessage(feedbackMessage);
        showSuccess('面试评价已生成');
      }

      // 🔥 新增：处理错误消息
      if (data.type === 'error') {
        console.error('❌ WebSocket error:', data.message);
        const errorMessage = {
          id: `error-${Date.now()}`,
          content: `系统错误：${data.message || '发生未知错误，请重试'}`,
          type: 'system' as const,
          timestamp: Date.now(),
          isFinal: true
        };
        addMessage(errorMessage);
        showError(data.message || '发生未知错误');
      }
    };

    // 监听WebSocket消息事件
    window.addEventListener('websocket-message', handleMessage as EventListener);

    return () => {
      // 清理监听器
      window.removeEventListener('websocket-message', handleMessage as EventListener);
    };
  }, [addMessage, showSuccess, setCurrentQuestionId]);

  // 🔥 Mock模式不再需要手动开始面试函数，已改为自动启动

  // 发送用户回答
  const handleSendMessage = useCallback((message: string) => {
    console.log('💬 Sending user answer:', message);

    // 🔥 立即显示等待提示toast（绿色）
    showSuccess('AI正在评估你的回答并生成下一个问题，请耐心等待', 20000); // 20秒显示时间

    // 添加用户消息到本地状态
    const userMessage = {
      id: `user-answer-${Date.now()}`,
      content: message,
      type: 'ai-suggestion' as const, // 用户回答显示为绿色气泡
      timestamp: Date.now(),
      isFinal: true
    };

    // 使用已存在的actions添加消息
    addMessage(userMessage);

    // 通过SessionManager发送用户回答
    const currentSessionId = sessionManager.getSessionId();
    if (isConnected && currentSessionId) {
      const answerMessage = {
        type: 'mock_interview_answer',
        sessionId: currentSessionId,
        questionId: session.currentQuestionId || `question-${Date.now()}`,
        answerText: message,
        timestamp: Date.now()
      };

      const webSocketManager = sessionManager.getWebSocketManager();
      webSocketManager.sendMessage(currentSessionId, answerMessage);
      console.log('📤 Sent answer message via SessionManager:', answerMessage);
    } else {
      console.error('❌ Session not connected or no session ID');
      showError('发送失败，请检查网络连接');
    }
  }, [session, isConnected, addMessage, showError, showSuccess, sessionManager]);

  // 组件卸载时清理（WebSocketManager会自动处理连接清理）
  useEffect(() => {
    return () => {
      console.log('🧹 MockInterviewSessionPage cleanup');
    };
  }, []);

  // 如果未登录，重定向到登录页面
  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // 🔥 优化：简化权限验证逻辑，减少延迟
  useEffect(() => {
    // 避免重复验证
    if (hasDeducted.current || !isAuthenticated) {
      return;
    }

    hasDeducted.current = true;

    // 简单检查是否从配置页面正常跳转而来
    if (!location.state || !location.state.sessionId) {
      console.warn('Invalid interview session, redirecting to config');
      navigate('/interview/mock/config');
      return;
    }

    // 🔥 优化：移除不必要的成功提示，减少UI更新
    console.log('✅ Interview session validated, ready to start');
  }, [isAuthenticated, location.state, navigate]);

  return (
    <>
      <div className="h-screen w-screen overflow-hidden bg-gray-100 flex flex-col">
        <div className="flex flex-col h-full bg-white">
          <Header
            companyName={session.companyName}
            positionName={session.positionName}
            elapsedTime={formattedTime}
            onEndInterview={endInterview}
          />

          <InterviewContent
            messages={messages}
            isListening={isListening}
            mode="mock"
            onSendMessage={handleSendMessage}
            isInterviewStarted={isInterviewStarted}
          />

          <ControlBar
            isListening={isListening}
            onToggleListening={toggleListening}
            onRefresh={refreshSession}
            mode="mock"
            onSendMessage={handleSendMessage}
            isInterviewStarted={isInterviewStarted}
            hasMessages={messages.length > 0}
          />
        </div>
      </div>
    </>
  );
};

export default MockInterviewSessionPage;
